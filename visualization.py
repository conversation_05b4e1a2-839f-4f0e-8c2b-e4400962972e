#!/usr/bin/env python3
"""
简单可视化对比跟踪结果与实际目标
"""

import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import argparse
import re

def load_simulation_data(csv_path):
    """加载模拟数据"""
    df = pd.read_csv(csv_path)
    return df

def parse_tracking_results(log_file):
    """从日志文件解析跟踪结果"""
    tracking_results = []

    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    current_group = None
    for line in lines:
        line = line.strip()

        # 解析组信息
        if "=== 处理第" in line and "组数据 ===" in line:
            try:
                group_num = int(line.split("第")[1].split("组")[0].strip())
                current_group = group_num
            except:
                pass

        # 解析轨迹信息
        if "轨迹ID:" in line:
            try:
                # 提取轨迹ID
                id_match = re.search(r'轨迹ID:\s*(\d+)', line)
                if not id_match:
                    continue
                track_id = int(id_match.group(1))

                # 提取位置坐标
                pos_match = re.search(r'位置:\s*\(([^)]+)\)', line)
                if not pos_match:
                    continue
                pos_coords = pos_match.group(1).split(', ')
                x, y, z = map(float, pos_coords)

                # 提取速度坐标
                vel_match = re.search(r'速度:\s*\(([^)]+)\)', line)
                if not vel_match:
                    continue
                vel_coords = vel_match.group(1).split(', ')
                vx, vy, vz = map(float, vel_coords)

                tracking_results.append({
                    'group': current_group,
                    'track_id': track_id,
                    'x': x, 'y': y, 'z': z,
                    'vx': vx, 'vy': vy, 'vz': vz
                })
            except Exception as e:
                print(f"解析错误，跳过行: {line}, 错误: {e}")
                continue

    return pd.DataFrame(tracking_results)

def create_simple_visualization(sim_data, track_data, output_path=None):
    """创建简单的可视化对比"""
    # 创建图形
    fig = plt.figure(figsize=(12, 10))

    # 3D位置对比
    ax1 = fig.add_subplot(221, projection='3d')
    ax1.scatter(sim_data['x'], sim_data['y'], sim_data['z'],
               c='blue', marker='o', alpha=0.5, s=30, label='Actual Targets')

    if not track_data.empty:
        ax1.scatter(track_data['x'], track_data['y'], track_data['z'],
                   c='red', marker='^', alpha=0.7, s=50, label='Tracking Results')

    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('Position Comparison (3D)')
    ax1.legend()

    # XY平面投影
    ax2 = fig.add_subplot(222)
    ax2.scatter(sim_data['x'], sim_data['y'], c='blue', marker='o', alpha=0.5, s=30, label='Actual Targets')
    if not track_data.empty:
        ax2.scatter(track_data['x'], track_data['y'], c='red', marker='^', alpha=0.7, s=50, label='Tracking Results')
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('XY Plane Projection')
    ax2.legend()
    ax2.grid(True)

    # 速度对比
    ax3 = fig.add_subplot(223, projection='3d')
    ax3.scatter(sim_data['vx'], sim_data['vy'], sim_data['vz'],
               c='blue', marker='o', alpha=0.5, s=30, label='Actual Velocities')

    if not track_data.empty:
        # 过滤掉零速度的跟踪结果
        non_zero_vel = track_data[(track_data['vx'] != 0) | (track_data['vy'] != 0) | (track_data['vz'] != 0)]
        if not non_zero_vel.empty:
            ax3.scatter(non_zero_vel['vx'], non_zero_vel['vy'], non_zero_vel['vz'],
                       c='red', marker='^', alpha=0.7, s=50, label='Tracking Velocities')

    ax3.set_xlabel('Vx (m/s)')
    ax3.set_ylabel('Vy (m/s)')
    ax3.set_zlabel('Vz (m/s)')
    ax3.set_title('Velocity Comparison (3D)')
    ax3.legend()

    # 统计信息
    ax4 = fig.add_subplot(224)
    ax4.axis('off')

    stats_text = f"""
Statistics:
Actual targets: {len(sim_data)}
Tracking results: {len(track_data)}
Tracking success rate: {len(track_data)/max(1,len(sim_data))*100:.1f}%

Actual target distribution:
X range: [{sim_data['x'].min():.1f}, {sim_data['x'].max():.1f}]
Y range: [{sim_data['y'].min():.1f}, {sim_data['y'].max():.1f}]
Z range: [{sim_data['z'].min():.1f}, {sim_data['z'].max():.1f}]

Velocity distribution:
Vx range: [{sim_data['vx'].min():.1f}, {sim_data['vx'].max():.1f}]
Vy range: [{sim_data['vy'].min():.1f}, {sim_data['vy'].max():.1f}]
Vz range: [{sim_data['vz'].min():.1f}, {sim_data['vz'].max():.1f}]
"""

    if not track_data.empty:
        stats_text += f"""
Tracking result distribution:
X range: [{track_data['x'].min():.1f}, {track_data['x'].max():.1f}]
Y range: [{track_data['y'].min():.1f}, {track_data['y'].max():.1f}]
Z range: [{track_data['z'].min():.1f}, {track_data['z'].max():.1f}]
"""

    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {output_path}")

    plt.show()

def main():
    parser = argparse.ArgumentParser(description='Simple visualization to compare tracking results with actual targets')
    parser.add_argument('--sim_data', default='data/radar_simulation_data.csv',
                       help='Path to simulation data CSV file')
    parser.add_argument('--log_file', default='tracking_log.txt',
                       help='Path to tracking log file')
    parser.add_argument('--output', default='tracking_comparison.png',
                       help='Path to output image file')

    args = parser.parse_args()

    # Load data
    print("Loading simulation data...")
    sim_data = load_simulation_data(args.sim_data)
    print(f"Loaded {len(sim_data)} actual target data points")

    # Parse tracking results
    print("Parsing tracking results...")
    try:
        track_data = parse_tracking_results(args.log_file)
        print(f"Parsed {len(track_data)} tracking results")
    except FileNotFoundError:
        print(f"Warning: Log file {args.log_file} not found, will only display actual target data")
        track_data = pd.DataFrame()

    # Generate visualization
    print("Generating visualization...")
    create_simple_visualization(sim_data, track_data, args.output)

if __name__ == "__main__":
    main()
