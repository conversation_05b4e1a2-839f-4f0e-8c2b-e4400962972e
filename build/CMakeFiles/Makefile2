# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/libSCR_5000_Alg.dir/all
all: CMakeFiles/MSHNet_TensorRT_Test.dir/all
all: CMakeFiles/test_algorithm.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/libSCR_5000_Alg.dir/clean
clean: CMakeFiles/MSHNet_TensorRT_Test.dir/clean
clean: CMakeFiles/test_algorithm.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/libSCR_5000_Alg.dir

# All Build rule for target.
CMakeFiles/libSCR_5000_Alg.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libSCR_5000_Alg.dir/build.make CMakeFiles/libSCR_5000_Alg.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libSCR_5000_Alg.dir/build.make CMakeFiles/libSCR_5000_Alg.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19 "Built target libSCR_5000_Alg"
.PHONY : CMakeFiles/libSCR_5000_Alg.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/libSCR_5000_Alg.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/libSCR_5000_Alg.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 0
.PHONY : CMakeFiles/libSCR_5000_Alg.dir/rule

# Convenience name for target.
libSCR_5000_Alg: CMakeFiles/libSCR_5000_Alg.dir/rule
.PHONY : libSCR_5000_Alg

# clean rule for target.
CMakeFiles/libSCR_5000_Alg.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libSCR_5000_Alg.dir/build.make CMakeFiles/libSCR_5000_Alg.dir/clean
.PHONY : CMakeFiles/libSCR_5000_Alg.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MSHNet_TensorRT_Test.dir

# All Build rule for target.
CMakeFiles/MSHNet_TensorRT_Test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10 "Built target MSHNet_TensorRT_Test"
.PHONY : CMakeFiles/MSHNet_TensorRT_Test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MSHNet_TensorRT_Test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/MSHNet_TensorRT_Test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 0
.PHONY : CMakeFiles/MSHNet_TensorRT_Test.dir/rule

# Convenience name for target.
MSHNet_TensorRT_Test: CMakeFiles/MSHNet_TensorRT_Test.dir/rule
.PHONY : MSHNet_TensorRT_Test

# clean rule for target.
CMakeFiles/MSHNet_TensorRT_Test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Test.dir/build.make CMakeFiles/MSHNet_TensorRT_Test.dir/clean
.PHONY : CMakeFiles/MSHNet_TensorRT_Test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_algorithm.dir

# All Build rule for target.
CMakeFiles/test_algorithm.dir/all: CMakeFiles/libSCR_5000_Alg.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=20,21 "Built target test_algorithm"
.PHONY : CMakeFiles/test_algorithm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_algorithm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_algorithm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles 0
.PHONY : CMakeFiles/test_algorithm.dir/rule

# Convenience name for target.
test_algorithm: CMakeFiles/test_algorithm.dir/rule
.PHONY : test_algorithm

# clean rule for target.
CMakeFiles/test_algorithm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_algorithm.dir/build.make CMakeFiles/test_algorithm.dir/clean
.PHONY : CMakeFiles/test_algorithm.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

