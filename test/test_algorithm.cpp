#include "libSCR_5000_Alg.hpp"
#include "utils.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <chrono>
#include <cstring>
#include <iomanip>
#include <algorithm>
#include <dirent.h>
#include <filesystem>

// 常量定义
const int ROWS = 1024;
const int COLS = 2048;

// 模拟读取连续3帧雷达数据的函数
bool LoadRadarDataFromFile(const std::vector<std::string>& file_paths,
                           std::vector<uint8_t>& merged_bytes) {
    if (file_paths.size() != 3) {
        std::cerr << "需提供 3 个文件路径" << std::endl;
        return false;
    }

    merged_bytes.clear();

    for (const auto& file_path : file_paths) {
        std::ifstream file(file_path, std::ios::binary | std::ios::ate);
        if (!file) {
            std::cerr << "无法打开文件: " << file_path << std::endl;
            return false;
        }

        std::streamsize sz = file.tellg();
        file.seekg(0, std::ios::beg);

        size_t prev = merged_bytes.size();
        merged_bytes.resize(prev + sz);
        file.read(reinterpret_cast<char*>(merged_bytes.data() + prev), sz);
    }

    std::cout << "成功合并 3 帧原始数据，总字节数: " << merged_bytes.size() << std::endl;
    return true;
}

// 从3帧雷达数据中提取指定帧数据,并提供S_head、S_data、D_head、D_data
bool ExtractFrameToStructured(const std::vector<uint8_t>& merged_bytes,
                              int frame_index,
                              GDDataPathObj_GPU& out) {
    constexpr size_t PULSE_CNT   = 1024;
    constexpr size_t HEADER_SIZE = sizeof(FrameHeader_Alg);
    constexpr size_t DATA_SIZE   = PULSE_CNT * COLS * 2 * sizeof(float);

    // 一帧布局：1024 S头 + S数据  + 1024 D头 + D数据
    constexpr size_t FRAME_TOTAL_SIZE =
        HEADER_SIZE * PULSE_CNT * 2 + DATA_SIZE * 2;

    const size_t FRAME_START_OFFSET = frame_index * FRAME_TOTAL_SIZE;

    if (merged_bytes.size() < FRAME_START_OFFSET + FRAME_TOTAL_SIZE) {
        std::cerr << "合并数据长度不足，无法提取第 " << frame_index << " 帧" << std::endl;
        return false;
    }

    out.rows = ROWS;
    out.cols = COLS;
    out.is_valid = true;
    out.S_head.clear();
    out.D_head.clear();
    out.S_data.clear();
    out.D_data.clear();

    const uint8_t* ptr = merged_bytes.data() + FRAME_START_OFFSET;

    /* 1. 1024 个 S 头 */
    const FrameHeader_Alg* s_hdr_ptr = reinterpret_cast<const FrameHeader_Alg*>(ptr);
    out.S_head.assign(s_hdr_ptr, s_hdr_ptr + PULSE_CNT);
    ptr += HEADER_SIZE * PULSE_CNT;

    /* 2. 整块 S 数据 */
    const float* s_data_ptr = reinterpret_cast<const float*>(ptr);
    out.S_data.assign(s_data_ptr, s_data_ptr + PULSE_CNT * COLS * 2);
    ptr += DATA_SIZE;

    /* 3. 1024 个 D 头 */
    const FrameHeader_Alg* d_hdr_ptr = reinterpret_cast<const FrameHeader_Alg*>(ptr);
    out.D_head.assign(d_hdr_ptr, d_hdr_ptr + PULSE_CNT);
    ptr += HEADER_SIZE * PULSE_CNT;

    /* 4. 整块 D 数据 */
    const float* d_data_ptr = reinterpret_cast<const float*>(ptr);
    out.D_data.assign(d_data_ptr, d_data_ptr + PULSE_CNT * COLS * 2);

    std::cout << "成功提取第 " << frame_index << " 帧结构化数据（重排格式）" << std::endl;
    return true;
}

// 打印检测结果
void PrintDetectionResults(const DetectionResult* results, int num_results) {
    std::cout << "\n=== 检测结果 ===" << std::endl;
    std::cout << "检测到 " << num_results << " 个目标" << std::endl;
    
    if (num_results > 0) {
        std::cout << std::left;
        std::cout << std::setw(8) << "num" 
                  << std::setw(10) << "X(m)" 
                  << std::setw(10) << "Y(m)" 
                  << std::setw(10) << "Z(m)"
                  << std::setw(10) << "Vx(m/s)" 
                  << std::setw(10) << "Vy(m/s)" 
                  << std::setw(10) << "Vz(m/s)"
                  << std::setw(10) << "range(m)" 
                  << std::setw(10) << "azim(°)" 
                  << std::setw(10) << "elev(°)" 
                  << std::setw(8) << "frame" << std::endl;
        
        for (int i = 0; i < std::min(num_results, 10); ++i) {  // 最多显示10个
            const auto& result = results[i];
            std::cout << std::setw(8) << i+1
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.x
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.y
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.z
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.vx
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.vy
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.vz
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.fMR
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.fMA
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.fME
                      << std::setw(8) << result.frame << std::endl;
        }
        
        if (num_results > 10) {
            std::cout << "... 还有 " << (num_results - 10) << " 个结果未显示" << std::endl;
        }
    }
}

// 打印跟踪结果
void PrintTrackingResults(const TrackingResult* results, int num_results) {
    std::cout << "\n=== 跟踪结果 ===" << std::endl;
    std::cout << "跟踪到 " << num_results << " 个目标" << std::endl;
    
    if (num_results > 0) {
        std::cout << std::left;
        std::cout << std::setw(8) << "ID" 
                  << std::setw(10) << "X(m)" 
                  << std::setw(10) << "Y(m)" 
                  << std::setw(10) << "Z(m)"
                  << std::setw(10) << "Vx(m/s)" 
                  << std::setw(10) << "Vy(m/s)" 
                  << std::setw(10) << "Vz(m/s)"
                  << std::setw(10) << "距离(m)" 
                  << std::setw(10) << "方位(°)" 
                  << std::setw(10) << "俯仰(°)" << std::endl;
        
        for (int i = 0; i < num_results; ++i) {
            const auto& result = results[i];
            std::cout << std::setw(8) << result.id
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.x
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.y
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.z
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.vx
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.vy
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.vz
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.fMR
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.fMA
                      << std::setw(10) << std::fixed << std::setprecision(2) << result.fME << std::endl;
        }
    }
}

int main() {
    std::cout << "=== SCR_5000算法库测试程序 ===" << std::endl;

    // 获取版本信息
    AlgorithmVersion version;
    GetVersionInfo(&version);
    std::cout << "算法库版本: " << version.version_string << std::endl;
    std::cout << "构建时间: " << version.build_date << std::endl;

    // 测试数据目录路径
    std::string test_data_dir = "data/test";

    std::cout << "\n正在扫描测试数据目录: " << test_data_dir << std::endl;

    // 获取所有bin文件
    auto bin_files = get_bin_files(test_data_dir);
    if (bin_files.empty()) {
        std::cerr << "未找到任何bin文件" << std::endl;
        return -1;
    }

    std::cout << "找到 " << bin_files.size() << " 个bin文件" << std::endl;

    // 确保文件数量是3的倍数
    if (bin_files.size() % 3 != 0) {
        std::cout << "警告: 文件数量不是3的倍数，将处理前 " << (bin_files.size() / 3) * 3 << " 个文件" << std::endl;
    }

    // 滑动窗口，每次 3 个连续文件
    const size_t window = 3;
    if (bin_files.size() < window) {
        std::cerr << "文件数量不足 3 个，无法形成任何窗口" << std::endl;
        return -1;
    }

    for (size_t start = 0; start + window <= bin_files.size(); ++start) {
        std::cout << "\n=== 处理滑动窗口 [" << (start + 1)
                << "-" << (start + window) << "] ===" << std::endl;

        std::vector<std::string> current_group;
        for (size_t i = 0; i < window; ++i) {
            current_group.push_back(bin_files[start + i]);
            std::cout << "文件 " << (i + 1) << ": "
                    << std::filesystem::path(bin_files[start + i]).filename() << std::endl;
        }

        // 加载雷达数据
        std::vector<uint8_t> radar_data;
        if (!LoadRadarDataFromFile(current_group, radar_data)) {
            std::cerr << "加载第 " << (start + 1) << " 组雷达数据失败" << std::endl;
            continue;
        }

        // 提取第一帧数据进行处理（可以根据需要修改为处理其他帧）
        GDDataPathObj_GPU processed_data;
        if (!ExtractFrameToStructured(radar_data, 0, processed_data)) {
            std::cerr << "提取第 " << (start + 1) << " 组第一帧数据失败" << std::endl;
            continue;
        }

        // 执行目标检测
        std::cout << "\n开始执行目标检测..." << std::endl;
        DetectionResult* detection_results = nullptr;
        int num_detections = 0;

        auto start_time = std::chrono::high_resolution_clock::now();
        int detection_status = TargetDetection(&processed_data, &detection_results, &num_detections);
        auto end_time = std::chrono::high_resolution_clock::now();

        auto detection_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        std::cout << "目标检测耗时: " << detection_duration.count() << " ms" << std::endl;

        if (detection_status != 0) {
            std::cerr << "目标检测失败，错误码: " << detection_status << std::endl;
            continue;
        }

        PrintDetectionResults(detection_results, num_detections);

        // 执行目标跟踪
        if (num_detections > 0) {
            std::cout << "\n开始执行目标跟踪..." << std::endl;
            TrackingResult* tracking_results = nullptr;
            int num_tracks = 0;

            start_time = std::chrono::high_resolution_clock::now();
            int tracking_status = TargetTracking(detection_results, num_detections, &tracking_results, &num_tracks);
            end_time = std::chrono::high_resolution_clock::now();

            auto tracking_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            std::cout << "目标跟踪耗时: " << tracking_duration.count() << " ms" << std::endl;

            if (tracking_status != 0) {
                std::cerr << "目标跟踪失败，错误码: " << tracking_status << std::endl;
            } else {
                PrintTrackingResults(tracking_results, num_tracks);
            }

            // 释放跟踪结果内存
            if (tracking_results) {
                ReleaseTrackingResults(tracking_results);
            }
        }

        // 释放检测结果内存
        if (detection_results) {
            ReleaseDetectionResults(detection_results);
        }

        std::cout << "第 " << (start + 1) << " 组处理完成" << std::endl;
    }

    // 释放所有资源
    std::cout << "\n释放算法库资源..." << std::endl;
    ReleaseAllResources();

    std::cout << "\n所有测试完成！" << std::endl;
    return 0;
}
