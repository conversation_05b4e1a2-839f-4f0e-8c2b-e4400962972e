#include "postprocess.hpp"

void binarize(const float* prob, cv::Mat& press, int rows, int cols) {
    #pragma omp parallel for
    for (int y = 0; y < rows; ++y) {
        uchar* ptr = press.ptr<uchar>(y);
        for (int x = 0; x < cols; ++x) {
            ptr[x] = (prob[y * cols + x] > 0) ? 255 : 0;
        }
    }
}

// std::vector<std::pair<float, float>> post_process(const float* prob, int rows, int cols) {
//     cv::Mat press(rows, cols, CV_8U, cv::Scalar(0));
//     binarize(prob, press, rows, cols);
//     cv::Mat labels, stats, centroids;
//     int num_labels = cv::connectedComponentsWithStats(press, labels, stats, centroids);

//     std::vector<std::pair<float, float>> result;
//     for (int i = 1; i < num_labels; ++i) {
//         if (stats.at<int>(i, cv::CC_STAT_AREA) > 1) {
//             result.emplace_back(centroids.at<double>(i, 0), centroids.at<double>(i, 1));
//         }
//     }
//     return result;
// }

std::vector<std::pair<int, int>> post_process(const float* prob, int rows, int cols) {
    cv::Mat press(rows, cols, CV_8U, cv::Scalar(0));
    binarize(prob, press, rows, cols);
    cv::Mat labels, stats, centroids;
    int num_labels = cv::connectedComponentsWithStats(press, labels, stats, centroids);

    std::vector<std::pair<int, int>> test_points;  // 改为返回 std::pair<int, int>
    for (int i = 1; i < num_labels; ++i) {
        if (stats.at<int>(i, cv::CC_STAT_AREA) > 1) {
            // 将 centroids 的 (float, float) 坐标转换为 (int, int)，并 ×2
            int x = static_cast<int>(std::round(centroids.at<double>(i, 0) * 2));
            int y = static_cast<int>(std::round(centroids.at<double>(i, 1) * 2));
            test_points.emplace_back(x, y);
        }
    }
    return test_points;
}