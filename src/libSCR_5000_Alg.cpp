#include "libSCR_5000_Alg.hpp"
#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "utils.hpp"
#include "PointTracker.hpp"
#include "logger.hpp"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <memory>
#include <ctime>

// 全局变量用于存储TensorRT资源
static nvinfer1::IRuntime* g_runtime = nullptr;
static nvinfer1::ICudaEngine* g_engine = nullptr;
static nvinfer1::IExecutionContext* g_context = nullptr;
static cudaStream_t g_stream;
static void* g_buffers[2] = {nullptr, nullptr};
static std::vector<float> g_input_tensor;
static std::vector<float> g_output_prob;
static std::vector<float> g_sample_input;
static bool g_initialized = false;

// 全局变量用于存储跟踪器
static std::unique_ptr<PointTracker> g_tracker = nullptr;
static std::vector<Point> g_current_group_detections;
static int g_group_start_frame = -1;
static const int FRAMES_PER_GROUP = 120;
static float g_prev_azimuth = -999.0f;
static bool g_azimuth_unchanged = false;

ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info) {
    static const AlgorithmVersion version = {
        1, 0, 0,
        "1.0.0",
        __DATE__ " " __TIME__
    };
    if (version_info) {
        *version_info = version;
    }
}

// 内部函数：初始化TensorRT引擎
static int InitializeTensorRT(const std::string& engine_path) {
    if (g_initialized) {
        return 0; // 已经初始化
    }

    try {
        // 初始化日志
        initLogger();

        // 初始化自定义插件
        initializeCustomPlugins();

        // 加载引擎
        g_engine = loadEngine(engine_path, g_runtime);
        if (!g_engine) {
            spdlog::error("Failed to load TensorRT engine");
            return -1;
        }

        g_context = g_engine->createExecutionContext();
        if (!g_context) {
            spdlog::error("Failed to create execution context");
            return -1;
        }

        // 创建CUDA流
        cudaError_t cuda_status = cudaStreamCreate(&g_stream);
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to create CUDA stream: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        // 获取输入输出维度
        const int inputIndex = 0, outputIndex = 1;
        const auto input_dims = g_engine->getBindingDimensions(inputIndex);
        const auto output_dims = g_engine->getBindingDimensions(outputIndex);

        const int input_h = input_dims.d[2];
        const int input_w = input_dims.d[3];
        const size_t input_size = input_dims.d[4] * input_h * input_w;
        const size_t output_size = output_dims.d[2] * output_dims.d[3];

        spdlog::info("Input shape: [{}x{}x{}]", input_dims.d[1], input_dims.d[2], input_dims.d[3]);

        // 分配GPU内存
        cuda_status = cudaMalloc(&g_buffers[inputIndex], input_size * sizeof(float));
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to allocate input buffer: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        cuda_status = cudaMalloc(&g_buffers[outputIndex], output_size * sizeof(float));
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to allocate output buffer: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        // 分配CPU内存
        g_input_tensor.resize(input_size);
        g_output_prob.resize(output_size);
        g_sample_input.resize(1024 * 1024 * 2);

        // 加载查表数据
        std::vector<std::string> table_paths = {
            "data/hecha_table.csv",
            "../data/hecha_table.csv",
            "../../data/hecha_table.csv"
        };

        bool table_loaded = false;
        for (const auto& path : table_paths) {
            std::ifstream test_file(path);
            if (test_file.good()) {
                spdlog::info("Found hecha table at: {}", path);
                loadHechaTable_(path);
                table_loaded = true;
                break;
            }
        }

        if (!table_loaded) {
            spdlog::warn("Could not find hecha_table.csv, using default values");
        }

        // 初始化跟踪器
        g_tracker = std::make_unique<PointTracker>(3, 20, 50.0f, 2);

        g_initialized = true;
        spdlog::info("TensorRT engine initialized successfully");
        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception during TensorRT initialization: {}", e.what());
        return -1;
    }
}

// 内部函数：将FrameHeader_Alg转换为FrameHeader
static std::vector<FrameHeader> ConvertFrameHeaders(const std::vector<FrameHeader_Alg>& alg_headers) {
    std::vector<FrameHeader> headers;
    headers.reserve(alg_headers.size());

    for (const auto& alg_header : alg_headers) {
        FrameHeader header;
        header.marker = alg_header.marker;
        header.channel = alg_header.channel;
        header.circle_num = alg_header.circle_num;
        header.frame_num = alg_header.frame_num;
        header.task_type = alg_header.task_type;
        header.group_id = alg_header.group_id;
        header.group_limit = alg_header.group_limit;
        header.pulse_id = alg_header.pulse_id;
        header.waveform = alg_header.waveform;
        header.waveform_type = alg_header.waveform_type;
        header.frame_length = alg_header.frame_length;
        header.angle = alg_header.angle;
        header.angle_counter = alg_header.angle_counter;
        std::memcpy(header.reserved, alg_header.reserved, sizeof(header.reserved));
        headers.push_back(header);
    }

    return headers;
}

// 目标检测算法实现
ALGORITHM_API int TargetDetection(
    const GDDataPathObj_GPU* input_data,
    DetectionResult** detection_results,
    int* num_detections
) {
    if (!input_data || !detection_results || !num_detections) {
        spdlog::error("Invalid input parameters for TargetDetection");
        return -1;
    }

    // 检查输入数据有效性
    if (!input_data->is_valid || input_data->S_data.empty() || input_data->D_data.empty()) {
        spdlog::error("Invalid input data");
        return -1;
    }

    // 初始化TensorRT引擎（如果尚未初始化）
    if (!g_initialized) {
        // 尝试多个可能的路径
        std::vector<std::string> engine_paths = {
            "data/trt_net_fp16_v1.trt",
            "../data/trt_net_fp16_v1.trt",
            "../../data/trt_net_fp16_v1.trt"
        };

        int init_result = -1;
        for (const auto& path : engine_paths) {
            std::ifstream test_file(path);
            if (test_file.good()) {
                spdlog::info("Found TensorRT engine at: {}", path);
                init_result = InitializeTensorRT(path);
                break;
            }
        }

        if (init_result != 0) {
            spdlog::error("Failed to initialize TensorRT engine");
            return -1;
        }
    }

    try {
        // 转换帧头格式
        std::vector<FrameHeader> S_head = ConvertFrameHeaders(input_data->S_head);
        // std::vector<FrameHeader> D_head = ConvertFrameHeaders(input_data->D_head);

        // 预处理：采样和归一化
        auto t0 = std::chrono::high_resolution_clock::now();
        sample_and_normalize(input_data->S_data, g_sample_input);
        auto t1 = std::chrono::high_resolution_clock::now();

        // 上传数据到GPU
        const int inputIndex = 0, outputIndex = 1;
        cudaError_t cuda_status = cudaMemcpyAsync(
            g_buffers[inputIndex],
            g_sample_input.data(),
            g_sample_input.size() * sizeof(float),
            cudaMemcpyHostToDevice,
            g_stream
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy input data to GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }
        cudaStreamSynchronize(g_stream);
        auto t2 = std::chrono::high_resolution_clock::now();

        // 执行推理
        bool inference_result = g_context->enqueueV2(g_buffers, g_stream, nullptr);
        if (!inference_result) {
            spdlog::error("TensorRT inference failed");
            return -1;
        }
        cudaStreamSynchronize(g_stream);
        auto t3 = std::chrono::high_resolution_clock::now();

        // 下载结果
        cuda_status = cudaMemcpyAsync(
            g_output_prob.data(),
            g_buffers[outputIndex],
            g_output_prob.size() * sizeof(float),
            cudaMemcpyDeviceToHost,
            g_stream
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy output data from GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }
        cudaStreamSynchronize(g_stream);
        auto t4 = std::chrono::high_resolution_clock::now();

        // 后处理：获取检测中心点
        auto centers = post_process(g_output_prob.data(), 512, 1024);
        auto t5 = std::chrono::high_resolution_clock::now();

        // 转换为复数格式进行俯仰角计算
        auto S_complex = convertToComplex(input_data->S_data);
        auto D_complex = convertToComplex(input_data->D_data);

        // 计算俯仰角、方位角、距离等信息
        auto results = computeElevationAngles_(S_head, S_complex, D_complex, centers);

        if (results.empty()) {
            spdlog::warn("No detection results");
            *detection_results = nullptr;
            *num_detections = 0;
            return 0;
        }

        // 分配输出内存
        *num_detections = results.size();
        *detection_results = new DetectionResult[*num_detections];

        spdlog::info("检测到 {} 个目标", *num_detections);

        // 填充检测结果
        for (size_t i = 0; i < results.size(); ++i) {
            const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] = results[i];

            (*detection_results)[i].x = x;
            (*detection_results)[i].y = y;
            (*detection_results)[i].z = z;
            (*detection_results)[i].vx = vx;
            (*detection_results)[i].vy = vy;
            (*detection_results)[i].vz = vz;
            (*detection_results)[i].fMV = fMV;
            (*detection_results)[i].fMR = fMR;
            (*detection_results)[i].fMA = fMA;
            (*detection_results)[i].fME = fME;
            (*detection_results)[i].frame = frame;
            (*detection_results)[i].row = row;
            (*detection_results)[i].col = col;
            (*detection_results)[i].type = 1; // 默认类型
        }

        spdlog::info("{:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10}",
                     "Vx(m/s)", "Vy(m/s)", "Vz(m/s)", "X(m)", "Y(m)", "Z(m)", "Velo", "Range", "Amaz", "Elev", "Frame", "X_cor", "Y_cor");

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            spdlog::info("{:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10.2f} {:<10} {:<10} {:<10}",
                        vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col);
        }

        // 记录性能信息
        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_gpuin = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_inf = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2).count();
        int t_gpuout = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t5 - t4).count();
        int total = t_pre + t_gpuin + t_gpuout + t_post + t_inf / 1000;

        spdlog::info("目标检测耗时(ms): 预处理:{} 上传:{} 推理:{}us 下载:{} 后处理:{} 总:{} (FPS:{:.2f})",
                     t_pre, t_gpuin, t_inf, t_gpuout, t_post, total, 1000.0 / total);
        
        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetDetection: {}", e.what());
        return -1;
    }
}

// 目标跟踪算法实现
ALGORITHM_API int TargetTracking(
    const DetectionResult* detection_results,
    int num_detections,
    TrackingResult** tracking_results,
    int* num_tracks
) {
    if (!detection_results || !tracking_results || !num_tracks) {
        spdlog::error("Invalid input parameters for TargetTracking");
        return -1;
    }

    if (num_detections <= 0) {
        spdlog::warn("No detections to track");
        *tracking_results = nullptr;
        *num_tracks = 0;
        return 0;
    }

    // 确保跟踪器已初始化
    if (!g_tracker) {
        g_tracker = std::make_unique<PointTracker>(3, 20, 50.0f, 2);
        spdlog::info("Tracker initialized");
    }

    try {
        // 将检测结果转换为Point格式
        std::vector<Point> current_detections;
        current_detections.reserve(num_detections);

        for (int i = 0; i < num_detections; ++i) {
            Point p;
            p.position[0] = detection_results[i].x;
            p.position[1] = detection_results[i].y;
            p.position[2] = detection_results[i].z;
            p.velocity[0] = detection_results[i].vx;
            p.velocity[1] = detection_results[i].vy;
            p.velocity[2] = detection_results[i].vz;
            p.type = detection_results[i].type;
            p.frame = detection_results[i].frame;
            p.label = -1; // 初始化标签
            current_detections.push_back(p);
        }

        // 检查是否需要处理分组逻辑
        int current_frame = detection_results[0].frame;
        float current_azimuth = detection_results[0].fMA;

        // 检查方位角是否变化
        g_azimuth_unchanged = (g_prev_azimuth != -999.0f) &&
                              (std::abs(current_azimuth - g_prev_azimuth) < 1.0f);
        g_prev_azimuth = current_azimuth;

        // 添加到当前组
        for (const auto& detection : current_detections) {
            g_current_group_detections.push_back(detection);
        }

        if (g_group_start_frame == -1) {
            g_group_start_frame = current_frame;
        }

        // 判断是否需要处理当前组
        bool should_process_group = false;
        if (g_azimuth_unchanged) {
            should_process_group = true;
            spdlog::info("方位角未变化，立即处理当前组");
        } else {
            int frame_diff = current_frame - g_group_start_frame;
            if (frame_diff < 0) frame_diff += 65536; // 处理帧号回绕
            if (frame_diff >= FRAMES_PER_GROUP - 1) {
                should_process_group = true;
            }
        }

        std::vector<TrackResult> tracks;

        if (should_process_group && !g_current_group_detections.empty()) {
            spdlog::info("处理跟踪组: 检测数量={}, 起始帧={}, 结束帧={}",
                        g_current_group_detections.size(), g_group_start_frame, current_frame);

            // 聚类检测结果
            auto clustered = clusterDetections(g_current_group_detections, 10.0f);
            spdlog::info("聚类后数量: {}", clustered.size());

            // 执行跟踪
            tracks = g_tracker->update(clustered);
            spdlog::info("跟踪结果数量: {}", tracks.size());

            // 轨迹插值（可选）
            g_tracker->interpolateTracks(1.0f, [&](const std::vector<TrackResult>& current_step) {
                // 处理插值结果（这里可以根据需要添加处理逻辑）
                spdlog::debug("插值步骤产生 {} 个轨迹点", current_step.size());
            });

            // 清理当前组
            g_current_group_detections.clear();
            g_group_start_frame = g_azimuth_unchanged ? -1 : current_frame + 1;
        } else {
            // 如果不需要处理组，返回空结果
            spdlog::debug("累积检测数据，当前组大小: {}", g_current_group_detections.size());
            *tracking_results = nullptr;
            *num_tracks = 0;
            return 0;
        }

        // 转换跟踪结果
        if (tracks.empty()) {
            *tracking_results = nullptr;
            *num_tracks = 0;
            return 0;
        }

        // 分配输出内存
        *num_tracks = tracks.size();
        *tracking_results = new TrackingResult[*num_tracks];

        // 填充跟踪结果
        for (size_t i = 0; i < tracks.size(); ++i) {
            const auto& track = tracks[i];

            (*tracking_results)[i].id = track.id;
            (*tracking_results)[i].x = track.position[0];
            (*tracking_results)[i].y = track.position[1];
            (*tracking_results)[i].z = track.position[2];
            (*tracking_results)[i].vx = track.velocity[0];
            (*tracking_results)[i].vy = track.velocity[1];
            (*tracking_results)[i].vz = track.velocity[2];

            // 计算转换值
            float range = std::sqrt(track.position[0]*track.position[0] +
                                  track.position[1]*track.position[1] +
                                  track.position[2]*track.position[2]);
            (*tracking_results)[i].fMR = range;
            (*tracking_results)[i].fMV = (track.position[0]*track.velocity[0] +
                                         track.position[1]*track.velocity[1] +
                                         track.position[2]*track.velocity[2]) / (range + 1e-6f);
            (*tracking_results)[i].fMA = std::fmod(std::atan2(track.position[1], track.position[0]) * 180.0f / M_PI + 360.0f, 360.0f);
            (*tracking_results)[i].fME = std::atan2(track.position[2], std::sqrt(track.position[0]*track.position[0] + track.position[1]*track.position[1])) * 180.0f / M_PI;

            // 设置默认值
            (*tracking_results)[i].fSNR = 1.0f;
            (*tracking_results)[i].fEn = 1.0f;
            (*tracking_results)[i].fRcs = 1.0f;
            (*tracking_results)[i].type = 1;
            (*tracking_results)[i].FPGATimeLog = 1;
            (*tracking_results)[i].PreShow = 2;
        }

        spdlog::info("跟踪完成，输出 {} 个轨迹", *num_tracks);
        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetTracking: {}", e.what());
        return -1;
    }
}

// 释放检测结果内存
ALGORITHM_API void ReleaseDetectionResults(DetectionResult* detection_results) {
    if (detection_results) {
        delete[] detection_results;
        spdlog::debug("Detection results memory released");
    }
}

// 释放跟踪结果内存
ALGORITHM_API void ReleaseTrackingResults(TrackingResult* tracking_results) {
    if (tracking_results) {
        delete[] tracking_results;
        spdlog::debug("Tracking results memory released");
    }
}

// 释放所有资源
ALGORITHM_API void ReleaseAllResources() {
    try {
        // 释放GPU内存
        if (g_buffers[0]) {
            cudaFree(g_buffers[0]);
            g_buffers[0] = nullptr;
            spdlog::debug("Input GPU buffer released");
        }

        if (g_buffers[1]) {
            cudaFree(g_buffers[1]);
            g_buffers[1] = nullptr;
            spdlog::debug("Output GPU buffer released");
        }

        // 销毁CUDA流
        if (g_stream) {
            cudaStreamDestroy(g_stream);
            g_stream = nullptr;
            spdlog::debug("CUDA stream destroyed");
        }

        // 释放TensorRT资源
        if (g_context) {
            g_context->destroy();
            g_context = nullptr;
            spdlog::debug("TensorRT execution context destroyed");
        }

        if (g_engine) {
            g_engine->destroy();
            g_engine = nullptr;
            spdlog::debug("TensorRT engine destroyed");
        }

        if (g_runtime) {
            g_runtime->destroy();
            g_runtime = nullptr;
            spdlog::debug("TensorRT runtime destroyed");
        }

        // 清理CPU内存
        g_input_tensor.clear();
        g_output_prob.clear();
        g_sample_input.clear();

        // 重置跟踪器
        g_tracker.reset();
        g_current_group_detections.clear();
        g_group_start_frame = -1;
        g_prev_azimuth = -999.0f;
        g_azimuth_unchanged = false;

        // 重置初始化标志
        g_initialized = false;

        spdlog::info("All resources released successfully");

    } catch (const std::exception& e) {
        spdlog::error("Exception during resource cleanup: {}", e.what());
    }
}

