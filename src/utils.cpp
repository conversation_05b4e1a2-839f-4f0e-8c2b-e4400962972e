#include "utils.hpp"
#include "logger.hpp"

#include <fstream>
#include <complex>
#include <cstring>
#include <dirent.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <NvInferPlugin.h>
#include <fftw3.h>
#include <cmath>
#include <random>

const int ROWS = 1024;
const int COLS = 2048;

// 查表全局变量定义
std::vector<std::vector<float>> hecha_table;
std::vector<float> x_axis;
std::vector<float> offsets = {2.5f, 7.5f, 12.5f, 20.0f, 31.25f};
std::vector<std::vector<float>> rang_limits = {{0, 5}, {5, 10}, {10, 15}, {15, 25}, {25, 37.5}};

int extractNumber(const std::string& str) {
    std::string numberStr;
    for (char c : str) if (isdigit(c)) numberStr += c;
    return numberStr.empty() ? 0 : std::stoi(numberStr);
}

// bool compareFilesByNumber(const std::string& a, const std::string& b) {
//     return extractNumber(a) < extractNumber(b);
// }

bool compareFilesByNumber(const std::string& a, const std::string& b) {
    // 提取文件名（去掉路径和扩展名）
    auto extractBaseName = [](const std::string& path) {
        size_t lastSlash = path.find_last_of("/\\");
        size_t lastDot = path.find_last_of('.');
        if (lastDot == std::string::npos) lastDot = path.size();
        return path.substr(lastSlash + 1, lastDot - lastSlash - 1);
    };

    std::string baseA = extractBaseName(a);
    std::string baseB = extractBaseName(b);

    // 按 '_' 分割字符串，逐个比较数字部分
    size_t posA = 0, posB = 0;
    while (posA < baseA.size() && posB < baseB.size()) {
        // 提取数字部分
        auto getNumber = [](const std::string& s, size_t& pos) {
            long num = 0;
            while (pos < s.size() && isdigit(s[pos])) {
                num = num * 10 + (s[pos] - '0');
                pos++;
            }
            if (pos < s.size() && s[pos] == '_') pos++; // 跳过分隔符
            return num;
        };

        long numA = getNumber(baseA, posA);
        long numB = getNumber(baseB, posB);

        if (numA != numB) {
            return numA < numB;
        }
    }

    // 如果前缀相同，按字符串长度排序（可选）
    return baseA.size() < baseB.size();
}

std::vector<std::string> get_bin_files(const std::string& folder_path) {
    std::vector<std::string> bin_files;
    DIR* dir = opendir(folder_path.c_str());
    if (!dir) {
        std::cerr << "Cannot open directory: " << folder_path << std::endl;
        return bin_files;
    }
    dirent* ent;
    while ((ent = readdir(dir))) {
        std::string name(ent->d_name);
        if (name.size() > 4 && name.substr(name.size() - 4) == ".bin")
            bin_files.push_back(folder_path + "/" + name);
    }
    closedir(dir);
    std::sort(bin_files.begin(), bin_files.end(), compareFilesByNumber);
    return bin_files;
}

std::string to_string_6digits(int i) {
    std::ostringstream oss;
    oss << std::setw(6) << std::setfill('0') << i;
    return oss.str();
}

void initializeCustomPlugins() {
    static bool initialized = false;
    if (!initialized) {
        initLibNvInferPlugins(nullptr, "");
        initialized = true;
    }
}

void readDualFrame(const std::string& path,
                   std::vector<FrameHeader>& S_head,   // 改成数组
                   std::vector<FrameHeader>& D_head,   // 改成数组
                   std::vector<float>& S_data,
                   std::vector<float>& D_data)
{
    constexpr size_t PULSE_CNT = 1024;                 // 1024 个脉冲
    constexpr size_t FRAME_HEADER_SZ = sizeof(FrameHeader); // 40
    constexpr size_t FRAME_DATA_SZ   = ROWS * COLS * 2 * sizeof(float);

    std::ifstream file(path, std::ios::binary);
    if (!file) throw std::runtime_error("无法打开bin文件");

    /* 1. 读 1024 个 S 帧头 */
    S_head.resize(PULSE_CNT);
    file.read(reinterpret_cast<char*>(S_head.data()),
              PULSE_CNT * FRAME_HEADER_SZ);

    /* 2. 读 1024 帧 S 数据 */
    S_data.resize(ROWS * COLS * 2);
    file.read(reinterpret_cast<char*>(S_data.data()),FRAME_DATA_SZ);

    /* 3. 读 1024 个 D 帧头 */
    D_head.resize(PULSE_CNT);
    file.read(reinterpret_cast<char*>(D_head.data()),
              PULSE_CNT * FRAME_HEADER_SZ);

    /* 4. 读 1024 帧 D 数据 */
    D_data.resize(ROWS * COLS * 2);
    file.read(reinterpret_cast<char*>(D_data.data()),FRAME_DATA_SZ);
}

// 3帧雷达数据解析


// 将一维实虚交错数据转换为二维复数矩阵
std::vector<std::vector<std::complex<float>>>
convertToComplex(const std::vector<float>& realImagData) {
    if (realImagData.size() != ROWS * COLS * 2)
        throw std::runtime_error("输入数据大小与期望不符");

    std::vector<std::vector<std::complex<float>>> complexData(ROWS,
        std::vector<std::complex<float>>(COLS));

    for (int i = 0; i < ROWS; ++i) {
        for (int j = 0; j < COLS; ++j) {
            float real = realImagData[(i * COLS + j) * 2];
            float imag = realImagData[(i * COLS + j) * 2 + 1];
            complexData[i][j] = std::complex<float>(real, imag);
        }
    }
    return complexData;
}

// 查表函数实现
void loadHechaTable_(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) throw std::runtime_error("无法打开hecha表文件");

    hecha_table.clear();
    std::string line;
    while (std::getline(file, line)) {
        std::vector<float> row(5);
        sscanf(line.c_str(), "%f,%f,%f,%f,%f", &row[0], &row[1], &row[2], &row[3], &row[4]);
        hecha_table.push_back(row);
    }
    x_axis.clear();
    for (float x = X_START; x <= X_END + 1e-6; x += X_STEP)
        x_axis.push_back(x);
}

int getLineIndex_(int row_idx) {
    if (row_idx >= 1 && row_idx <= 512) return 0;
    if (row_idx > 512 && row_idx <= 768) return 1;
    if (row_idx > 768 && row_idx <= 896) return 2;
    if (row_idx > 896 && row_idx <= 960) return 3;
    if (row_idx > 960 && row_idx <= 1024) return 4;
    throw std::runtime_error("无效的行索引");
}

float findXWithOffset_(int line_idx, float y_target) {
    float min_diff = 1e9;
    int best_idx = 0;
    for (size_t i = 0; i < hecha_table.size(); ++i) {
        float diff = std::abs(hecha_table[i][line_idx] - y_target);
        if (diff < min_diff) {
            min_diff = diff;
            best_idx = i;
        }
    }
    float x_val = x_axis[best_idx] + offsets[line_idx];
    return std::min(std::max(x_val, rang_limits[line_idx][0]), rang_limits[line_idx][1]);
}

// FFT函数实现
void performFFT2D_(const std::vector<std::vector<std::complex<float>>>& data,
                  std::vector<std::vector<std::complex<float>>>& fft_result)
    {
        fft_result.resize(ROWS, std::vector<std::complex<float>>(COLS));
        fftwf_complex *in = (fftwf_complex*) fftwf_malloc(sizeof(fftwf_complex) * ROWS);
        fftwf_complex *out = (fftwf_complex*) fftwf_malloc(sizeof(fftwf_complex) * ROWS);
        fftwf_plan p = fftwf_plan_dft_1d(ROWS, in, out, FFTW_FORWARD, FFTW_ESTIMATE);

        // 对每列做FFT（沿着行方向）
        for (int col = 0; col < COLS; ++col) {
            for (int i = 0; i < ROWS; ++i) {
                in[i][0] = data[i][col].real();
                in[i][1] = data[i][col].imag();
            }
            fftwf_execute(p);
            for (int i = 0; i < ROWS; ++i)
                fft_result[i][col] = {out[i][0], out[i][1]};
        }
        fftwf_destroy_plan(p);
        fftwf_free(in);
        fftwf_free(out);
    }

// 速度换算函数实现
float calcDopplerSpeedFromIndex(int y_idx){
    constexpr float vel_per_bin = 0.1831f;
    const std::vector<int> sub_lengths = {512, 256, 128, 64, 64};

    int start = 0;
    for (int k = 0; k < sub_lengths.size(); ++k) {
        int len = sub_lengths[k];
        int end = start + len;

        if (y_idx >= start && y_idx < end) {
            int local_idx = y_idx - start;
            int half = len / 2;
            if (local_idx < half) {
                return local_idx * vel_per_bin;
            } else {
                return -(len - local_idx) * vel_per_bin;
            }
        }

        start = end;
    }

    return 0.0f;  // 越界保护
}


// 俯仰角、方位、距离计算函数实现
std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>>
computeElevationAngles_(
    const std::vector<FrameHeader>& S_head,
    const std::vector<std::vector<std::complex<float>>>& S_data,
    const std::vector<std::vector<std::complex<float>>>& D_data,
    const std::vector<std::pair<int, int>>& test_points)
    {
        std::vector<std::vector<std::complex<float>>> S_fft, D_fft;
        performFFT2D_(S_data, S_fft);
        performFFT2D_(D_data, D_fft);

        std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>> result;
        for (const auto& [row, col] : test_points) {
            auto val_s = S_fft[col][row];       //待检查坐标
            auto val_d = D_fft[col][row];       //
            float amp_s = std::abs(val_s);
            float amp_d = std::abs(val_d);
            float amp_ratio = amp_d / (amp_s + 1e-6);
            float phase_diff = std::signbit(std::cos(std::arg(val_d) - std::arg(val_s))) ? -1.f : 1.f;
            int line_idx = getLineIndex_(col);
            float angle = findXWithOffset_(line_idx, phase_diff * amp_ratio);
            float azimuth_deg = S_head[col].angle / 100.0f;
            uint16_t frame = S_head[col].frame_num;

            // 距离换算
            int range_bin = row;
            float range_bin_adj = (range_bin < 1535) ? range_bin + 512 : range_bin - 1535;
            float range_m = range_bin_adj * 3.0f;

            // 速度换算
            int doppler_bin = col;
            float velocity_m = calcDopplerSpeedFromIndex(doppler_bin);

            // 三维坐标换算
            float azimuth_rad = azimuth_deg * M_PI / 180.0f;
            float elevation_rad = angle * M_PI / 180.0f;
            float x_coord = range_m * std::cos(azimuth_rad) * std::cos(elevation_rad);
            float y_coord = range_m * std::sin(azimuth_rad) * std::cos(elevation_rad);
            float z_coord = range_m * std::sin(elevation_rad);

            // 方向速度换算
            float vx = velocity_m * std::cos(azimuth_rad) * std::cos(elevation_rad);
            float vy = velocity_m * std::sin(azimuth_rad) * std::cos(elevation_rad);
            float vz = velocity_m * std::sin(elevation_rad);
        
            result.emplace_back(
                vx,
                vy,
                vz,
                x_coord,
                y_coord,
                z_coord,
                velocity_m,
                range_m,
                azimuth_deg, 
                angle, 
                frame, 
                row, 
                col);
        }
        return result;
    }

// 聚类算法
std::vector<Point> clusterDetections(const std::vector<Point>& points, float eps) {
    if (points.empty()) {
        return std::vector<Point>();
    }

    std::vector<Point> clustered_points;
    std::vector<bool> used(points.size(), false);

    for (size_t i = 0; i < points.size(); ++i) {
        if (used[i]) continue;

        // 找到所有在eps距离内的点
        std::vector<size_t> cluster_indices;
        cluster_indices.push_back(i);
        used[i] = true;

        for (size_t j = i + 1; j < points.size(); ++j) {
            if (used[j]) continue;

            // 计算欧几里得距离
            float dx = points[i].position[0] - points[j].position[0];
            float dy = points[i].position[1] - points[j].position[1];
            float dz = points[i].position[2] - points[j].position[2];
            float dist = std::sqrt(dx * dx + dy * dy + dz * dz);

            if (dist < eps) {
                cluster_indices.push_back(j);
                used[j] = true;
            }
        }

        // 计算聚类中心的位置和速度
        std::array<float, 3> center_position = {0, 0, 0};
        std::array<float, 3> center_velocity = {0, 0, 0};
        
        for (size_t idx : cluster_indices) {
            center_position[0] += points[idx].position[0];
            center_position[1] += points[idx].position[1];
            center_position[2] += points[idx].position[2];
            
            center_velocity[0] += points[idx].velocity[0];
            center_velocity[1] += points[idx].velocity[1];
            center_velocity[2] += points[idx].velocity[2];
        }

        // 计算平均值
        float cluster_size = static_cast<float>(cluster_indices.size());
        center_position[0] /= cluster_size;
        center_position[1] /= cluster_size;
        center_position[2] /= cluster_size;
        
        center_velocity[0] /= cluster_size;
        center_velocity[1] /= cluster_size;
        center_velocity[2] /= cluster_size;

        // 创建聚类中心点
        Point cluster_center;
        cluster_center.position = center_position;
        cluster_center.velocity = center_velocity;
        cluster_center.type = points[i].type;    // 使用第一个点的类型
        cluster_center.frame = points[i].frame;  // 使用第一个点的帧数
        cluster_center.label = points[i].label;  // 使用第一个点的标签

        clustered_points.push_back(cluster_center);
    }

    return clustered_points;
}

// 匈牙利匹配算法
std::vector<std::pair<int, int>> hungarianMatch(const Eigen::MatrixXf& cost_matrix) {
    int n_rows = cost_matrix.rows();
    int n_cols = cost_matrix.cols();

    // 处理空矩阵的边界情况
    if (n_rows == 0 || n_cols == 0) {
        return std::vector<std::pair<int, int>>();
    }

    std::vector<std::pair<int, int>> matches;
    std::vector<bool> row_used(n_rows, false);
    std::vector<bool> col_used(n_cols, false);

    // 贪心匹配：每次选择最小代价的未匹配对
    for (int iter = 0; iter < std::min(n_rows, n_cols); ++iter) {
        float min_cost = std::numeric_limits<float>::max();
        int best_row = -1, best_col = -1;

        for (int i = 0; i < n_rows; ++i) {
            if (row_used[i]) continue;
            for (int j = 0; j < n_cols; ++j) {
                if (col_used[j]) continue;
                if (cost_matrix(i, j) < min_cost) {
                    min_cost = cost_matrix(i, j);
                    best_row = i;
                    best_col = j;
                }
            }
        }

        if (best_row != -1 && best_col != -1) {
            matches.emplace_back(best_row, best_col);
            row_used[best_row] = true;
            col_used[best_col] = true;
        } else {
            break;
        }
    }

    return matches;
}

// 模拟数据生成函数实现（用于测试）
std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>>
generateSimulatedResults(int num_targets, int frame_number, float noise_level) {
    std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>> results;

    // 随机数生成器
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_real_distribution<float> noise_dist(-noise_level, noise_level);
    std::uniform_real_distribution<float> pos_dist(-1000.0f, 1000.0f);  // 位置范围
    std::uniform_real_distribution<float> vel_dist(-50.0f, 50.0f);      // 速度范围
    std::uniform_real_distribution<float> angle_dist(0.0f, 360.0f);     // 角度范围
    std::uniform_real_distribution<float> elev_dist(-30.0f, 30.0f);     // 俯仰角范围
    std::uniform_int_distribution<int> coord_dist(100, 900);            // 坐标范围

    for (int i = 0; i < num_targets; ++i) {
        // 生成基础参数
        float base_x = pos_dist(gen);
        float base_y = pos_dist(gen);
        float base_z = pos_dist(gen);

        float base_vx = vel_dist(gen);
        float base_vy = vel_dist(gen);
        float base_vz = vel_dist(gen);

        // 添加噪声
        float x = base_x + noise_dist(gen);
        float y = base_y + noise_dist(gen);
        float z = base_z + noise_dist(gen);

        float vx = base_vx + noise_dist(gen) * 0.1f;
        float vy = base_vy + noise_dist(gen) * 0.1f;
        float vz = base_vz + noise_dist(gen) * 0.1f;

        // 计算径向参数
        float range = std::sqrt(x*x + y*y + z*z);
        float radial_velocity = (x*vx + y*vy + z*vz) / (range + 1e-6f);

        // 计算角度
        float azimuth = std::atan2(y, x) * 180.0f / M_PI;
        if (azimuth < 0) azimuth += 360.0f;

        float elevation = std::asin(z / (range + 1e-6f)) * 180.0f / M_PI;

        // 生成坐标
        int row = coord_dist(gen);
        int col = coord_dist(gen);

        // 创建结果tuple
        results.emplace_back(
            vx,                    // x方向速度
            vy,                    // y方向速度
            vz,                    // z方向速度
            x,                     // x坐标
            y,                     // y坐标
            z,                     // z坐标
            radial_velocity,       // 径向速度
            range,                 // 径向距离
            azimuth,               // 方位角
            elevation,             // 俯仰角
            static_cast<uint16_t>(frame_number), // 帧号
            row,                   // X坐标
            col                    // Y坐标
        );
    }

    return results;
}

// 从CSV文件读取模拟数据
std::vector<Point> loadSimulationDataFromCSV(const std::string& csv_path) {
    std::vector<Point> points;
    std::ifstream file(csv_path);

    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << csv_path << std::endl;
        return points;
    }

    std::string line;
    // 跳过标题行
    std::getline(file, line);

    while (std::getline(file, line)) {
        std::stringstream ss(line);
        std::string cell;
        std::vector<std::string> row;

        // 解析CSV行
        while (std::getline(ss, cell, ',')) {
            row.push_back(cell);
        }

        if (row.size() >= 12) {  // 确保有足够的列
            Point point;

            // 解析数据: vx,vy,vz,x,y,z,fMV,fMR,fMA,fME,type,frame
            point.velocity[0] = std::stof(row[0]);  // vx
            point.velocity[1] = std::stof(row[1]);  // vy
            point.velocity[2] = std::stof(row[2]);  // vz
            point.position[0] = std::stof(row[3]);  // x
            point.position[1] = std::stof(row[4]);  // y
            point.position[2] = std::stof(row[5]);  // z
            point.type = std::stoi(row[10]);        // type
            point.frame = std::stoi(row[11]);       // frame
            point.label = 0;                        // 初始化标签

            points.push_back(point);
        }
    }

    file.close();
    std::cout << "从CSV文件读取了 " << points.size() << " 个数据点" << std::endl;
    return points;
}

// 将单个 Track 转换为 TargetInfo
TargetInfo convertTrackToTargetInfo(const TrackResult & track) {
    TargetInfo target;
    target.id = track.id;
    target.x = track.position[0];
    target.y = track.position[1];
    target.z = track.position[2];
    target.vx = track.velocity[0];
    target.vy = track.velocity[1];
    target.vz = track.velocity[2];

    // 计算转换值
    target.fMR = std::sqrt(target.x*target.x + target.y*target.y + target.z*target.z);
    // target.fMV = std::sqrt(target.vx*target.vx + target.vy*target.vy + target.vz*target.vz);
    target.fMV = (target.x*target.vx + target.y*target.vy + target.z*target.vz) / (target.fMR + 1e-6f);
    target.fMA = std::fmod(std::atan2(target.y, target.x) * 180.0f / M_PI + 360.0f, 360.0f);
    target.fME = std::atan2(target.z, std::sqrt(target.x*target.x + target.y*target.y)) * 90.0f / M_PI;

    // 设置默认值
    target.fSNR = 1.0f;
    target.fEn = 1.0f;
    target.fRcs = 1.0f;
    target.FPGATimeLog = 1;
    target.PreShow = 2;
    target.type = 1;  // 默认类型

    return target;
}

// 将 tracks 向量转换为 TargetInfo 向量
std::vector<TargetInfo> convertTracksToTargetInfos(const std::vector<TrackResult>& tracks) {
    std::vector<TargetInfo> results;
    results.reserve(tracks.size());  // 预分配空间以提高效率
    
    for (const auto& track : tracks) {
        TargetInfo target = convertTrackToTargetInfo(track);
        
        // 打印信息（可选）
        spdlog::info("------ 目标 ID: {} ------", target.id);
        spdlog::info("位置 (x,y,z): ({:.2f}, {:.2f}, {:.2f})", target.x, target.y, target.z);
        spdlog::info("速度 (vx,vy,vz): ({:.2f}, {:.2f}, {:.2f})", target.vx, target.vy, target.vz);
        spdlog::info("径向距离 fMR: {:.2f}, 径向速度 fMV: {:.2f}", target.fMR, target.fMV);
        spdlog::info("方位角 fMA: {:.2f}°, 俯仰角 fME: {:.2f}°", target.fMA, target.fME);
        spdlog::info("信噪比 fSNR: {:.2f}, 峰值能量 fEn: {:.2f}", target.fSNR, target.fEn);
        spdlog::info("RCS: {:.2f}, 目标类型: {}", target.fRcs, target.type);
        spdlog::info("FPGA 时间戳: {}", target.FPGATimeLog);
        spdlog::info("预测显示标识 PreShow: {}", target.PreShow);
        spdlog::info("---------------------------------------------");

        results.push_back(target);
    }
    
    return results;
}