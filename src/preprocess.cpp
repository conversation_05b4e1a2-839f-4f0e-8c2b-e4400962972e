#include "preprocess.hpp"
#include <thread>
#include <algorithm>

void sample_data(const std::vector<double>& input, std::vector<double>& output, size_t sample_size) {
    const size_t stride = sample_size * 2;
    const size_t n = input.size() / stride * sample_size;
    output.resize(n);
    const double* input_data = input.data();
    double* output_data = output.data();

    for (size_t i = 0; i < n; ++i) {
        output_data[i] = input_data[(i / sample_size) * stride + (i % sample_size)];
    }
}

void normalize_chunk(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t start,
    size_t end,
    double real_offset,
    double real_scale_inv,
    double imag_offset,
    double imag_scale_inv
) {
    for (size_t i = start; i < end; i += 2) {
        output_tensor[i]     = static_cast<float>((input_tensor[i] - real_offset) * real_scale_inv);
        output_tensor[i + 1] = static_cast<float>((input_tensor[i + 1] - imag_offset) * imag_scale_inv);
    }
}

void normalize_input_tensor_multithreaded(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t num_threads
) {
    const double real_offset = 842827867;
    const double real_scale_inv = 1.0 / 1707641336123;
    const double imag_offset = -214253964;
    const double imag_scale_inv = 1.0 / 1699396044280;

    const size_t num_elements = input_tensor.size();
    output_tensor.resize(num_elements);
    const size_t chunk_size = (num_elements + num_threads - 1) / num_threads;

    std::vector<std::thread> threads;
    for (size_t i = 0; i < num_threads; ++i) {
        size_t start = i * chunk_size;
        size_t end = std::min(start + chunk_size, num_elements);
        if (start % 2 != 0) --start;

        threads.emplace_back(normalize_chunk,
            std::ref(input_tensor), std::ref(output_tensor),
            start, end, real_offset, real_scale_inv, imag_offset, imag_scale_inv
        );
    }

    for (auto& thread : threads) thread.join();
}

void sample_and_normalize(const std::vector<float>& input, std::vector<float>& output) {
    const size_t stride = 4;            // 输入每4个元素采2个 (实部+虚部)
    size_t total_blocks = input.size() / stride;
    output.resize(total_blocks * 2);    // 每块输出2个元素（归一化后的实部和虚部）

    // 归一化参数 (示例常量)
    const float real_offset = 842827867.0f;
    const float real_scale_inv = 1.0f / 1707641336123.0f;
    const float imag_offset = -214253964.0f;
    const float imag_scale_inv = 1.0f / 1699396044280.0f;

    size_t num_threads = std::thread::hardware_concurrency();
    std::vector<std::thread> threads;
    size_t blocks_per_thread = (total_blocks + num_threads - 1) / num_threads;

    for (size_t t = 0; t < num_threads; ++t) {
        size_t start_block = t * blocks_per_thread;
        size_t end_block = std::min(start_block + blocks_per_thread, total_blocks);
        threads.emplace_back([&, start_block, end_block]() {
            for (size_t blk = start_block; blk < end_block; ++blk) {
                size_t idx = blk * stride;
                // 直接按实部和虚部分别读取
                float real_val = input[idx];
                float imag_val = input[idx + 1];
                // 归一化并写入输出
                size_t out_idx = blk * 2;
                output[out_idx]     = (real_val - real_offset) * real_scale_inv;
                output[out_idx + 1] = (imag_val - imag_offset) * imag_scale_inv;
            }
        });
    }
    for (auto &th : threads) {
        if (th.joinable()) th.join();
    }
}
