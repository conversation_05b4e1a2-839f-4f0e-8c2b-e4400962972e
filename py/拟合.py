import numpy as np
import matplotlib.pyplot as plt
import scipy.io
# from matplotlib import font_manager

# plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
# plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_and_preprocess_data():
    """加载并预处理数据（只需运行一次）"""
    mat_data = scipy.io.loadmat('data/AthetaPhi.mat')
    AthetaPhi = mat_data['AthetaPhi']
    he_cha_bi_fuzhi = AthetaPhi.copy() * -1  # 整体取负
    hecha_table = np.column_stack((
        he_cha_bi_fuzhi[:, 5],  # 第6列 → 折线0
        he_cha_bi_fuzhi[:, 1],  # 第2列 → 折线1
        he_cha_bi_fuzhi[:, 0],  # 第1列 → 折线2
        he_cha_bi_fuzhi[:, 2],  # 第3列 → 折线3
        he_cha_bi_fuzhi[:, 3]   # 第4列 → 折线4
    ))
    return hecha_table

# 全局变量（避免重复加载数据）
hecha_table = load_and_preprocess_data()
x = np.arange(-3.6, 3.61, 0.1)  # x轴范围固定
offsets = [2.5, 7.5, 12.5, 20, 31.25]  # 各折线的固定偏移量

def find_x_with_offset(line_idx, y_target):
    """
    根据y值找到折线对应x值，添加固定偏移量后按指定范围截断
    
    参数:
        line_idx: 折线编号 (0~4)
        y_target: 目标y值
        
    返回:
        处理后的x值（按折线分段截断）
    """
    # 各折线的截断范围 [min, max]
    range_limits = [
        [0, 5],         # 折线0
        [5, 10],        # 折线1
        [10, 15],       # 折线2
        [15, 25],       # 折线3
        [25, 37.5]      # 折线4
    ]
    
    # 检查输入合法性
    if line_idx not in range(5):
        raise ValueError("折线编号必须是0~4的整数")
    
    # 直接匹配最近点
    y_data = hecha_table[:, line_idx]
    idx = np.argmin(np.abs(y_data - y_target))
    x_raw = x[idx]
    
    # 添加偏移量并截断到指定范围
    offset = offsets[line_idx]
    x_result = x_raw + offset
    x_result = np.clip(x_result, *range_limits[line_idx])  # 分段截断
    
    return x_result

# 可视化验证
def plot_results():
    plt.figure(figsize=(12, 6))
    for i in range(5):
        plt.plot(x, hecha_table[:, i], label=f'折线{i} (原始)')
        
        # 测试随机y值
        y_test = np.random.uniform(min(hecha_table[:, i]), max(hecha_table[:, i]))
        x_result = find_x_with_offset(i, y_test)
        
        # 标记结果点
        plt.scatter(x_result - offsets[i], y_test, color='red', s=100, 
                   label=f'折线{i}: y={y_test:.2f}→x={x_result:.2f}')
    
    plt.xlabel("原始x角度 (未加偏移)")
    plt.ylabel("y值")
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True)
    plt.tight_layout()
    plt.show()

# 示例使用
if __name__ == "__main__":
    
    range_limits = [
        [0, 5],         # 折线0
        [5, 10],        # 折线1
        [10, 15],       # 折线2
        [15, 25],       # 折线3
        [25, 37.5]      # 折线4
    ]
    
    # 测试案例
    test_cases = [
        (0, 0.1),   # 折线0 → 范围 [0,5]
        (1, -0.2),  # 折线1 → 范围 [5,10]
        (3, 0.4)    # 折线3 → 范围 [15,20]
    ]
    
    for line_idx, y_val in test_cases:
        result = find_x_with_offset(line_idx, y_val)
        print(f"折线{line_idx}: y={y_val} → 原始x={x[np.argmin(np.abs(hecha_table[:, line_idx] - y_val))]:.2f}° → "
              f"加偏移{offsets[line_idx]}°后={result:.2f}° (范围{range_limits[line_idx]})")

    # plot_results()  # 生成验证图