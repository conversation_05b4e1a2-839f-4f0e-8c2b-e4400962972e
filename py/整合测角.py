import numpy as np
import struct
import mkl_fft
import matplotlib.pyplot as plt
import scipy.io
import pandas as pd

# 帧头结构定义（40字节）
frame_header_struct = struct.Struct('<IHHHBBHHBBHIH14s')
SAMPLES_PER_FRAME = 2048  # 每帧2048个复数样本
BYTES_PER_FRAME = 40 + 4096 * 4 * 1024  # 帧头 + 一帧1024x2048 float32

# 全局变量（避免重复加载数据）
hecha_table = None
x = np.arange(-3.6, 3.61, 0.1)  # x轴范围固定
offsets = [2.5, 7.5, 12.5, 20, 31.25]  # 各折线的固定偏移量
range_limits = [
    [0, 5],         # 折线0
    [5, 10],        # 折线1
    [10, 15],       # 折线2
    [15, 25],       # 折线3
    [25, 37.5]      # 折线4
]

def load_and_preprocess_data():
    """加载并预处理数据（只需运行一次）"""
    global hecha_table
    mat_data = scipy.io.loadmat('data/AthetaPhi.mat')
    AthetaPhi = mat_data['AthetaPhi']
    he_cha_bi_fuzhi = AthetaPhi.copy() * -1  # 整体取负
    hecha_table = np.column_stack((
        he_cha_bi_fuzhi[:, 5],  # 第6列 → 折线0
        he_cha_bi_fuzhi[:, 1],  # 第2列 → 折线1
        he_cha_bi_fuzhi[:, 0],  # 第1列 → 折线2
        he_cha_bi_fuzhi[:, 2],  # 第3列 → 折线3
        he_cha_bi_fuzhi[:, 3]   # 第4列 → 折线4
    ))

    df = pd.DataFrame(hecha_table, columns=['line0', 'line1', 'line2', 'line3', 'line4'])
    df.to_csv('data/processed_data.csv', index=False)

    return hecha_table

def parse_frame_header(header_bytes):
    unpacked = frame_header_struct.unpack(header_bytes)
    header = {
        'marker': unpacked[0],
        'channel': unpacked[1],
        'circle_num': unpacked[2],
        'frame_num': unpacked[3],
        'task_type': unpacked[4],
        'group_id': unpacked[5],
        'group_limit': unpacked[6],
        'pulse_id': unpacked[7],
        'waveform': unpacked[8],
        'waveform_type': unpacked[9],
        'frame_length': unpacked[10],
        'angle': unpacked[11],
        'angle_counter': unpacked[12],
        'reserved': unpacked[13],
    }
    return header

def read_bin_dual_frame(file_path):
    """读取两个帧，返回 S_head, D_head, S_data, D_data"""
    with open(file_path, 'rb') as f:
        headers = []
        datas = []
        for _ in range(2):
            header_bytes = f.read(40)
            if len(header_bytes) < 40:
                raise ValueError("文件中帧头不足")
            header = parse_frame_header(header_bytes)
            headers.append(header)

            data_bytes = f.read(4096 * 4 * 1024)
            if len(data_bytes) < 4096 * 4 * 1024:
                raise ValueError("文件中帧数据不足")
            float_data = np.frombuffer(data_bytes, dtype=np.float32)
            complex_data = float_data[0::2] + 1j * float_data[1::2]
            complex_data = complex_data.reshape(1024, 2048)
            datas.append(complex_data)

    return headers[0], headers[1], datas[0], datas[1]

def apply_window(data_2d):
    """在列方向加窗（即对每一行加窗）"""
    window = np.hanning(data_2d.shape[1])  # 长度2048
    return data_2d * window[np.newaxis, :]  # broadcast to (1024, 2048)

def fft_2d(data_2d):
    """对二维复数矩阵的每列做FFT（行方向），结果 shape = (1024, 2048)"""
    return mkl_fft.fft(data_2d, axis=0)

def get_line_index(row_idx):
    """根据行索引确定对应的折线编号"""
    if 1 <= row_idx <= 512:
        return 0
    elif 512 < row_idx <= 512+256:
        return 1
    elif 512+256 < row_idx <= 512+256+128:
        return 2
    elif 512+256+128 < row_idx <= 512+256+128+64:
        return 3
    elif 512+256+128+64 < row_idx <= 512+256+128+64+64:
        return 4
    else:
        raise ValueError(f"无效的行索引: {row_idx}，必须在1~1024范围内")

def find_x_with_offset(line_idx, y_target):
    """
    根据y值找到折线对应x值，添加固定偏移量后按指定范围截断
    
    参数:
        line_idx: 折线编号 (0~4)
        y_target: 目标y值
        
    返回:
        处理后的x值（按折线分段截断）
    """
    if hecha_table is None:
        load_and_preprocess_data()
    
    # 检查输入合法性
    if line_idx not in range(5):
        raise ValueError("折线编号必须是0~4的整数")
    
    # 直接匹配最近点
    y_data = hecha_table[:, line_idx]
    idx = np.argmin(np.abs(y_data - y_target))
    x_raw = x[idx]
    
    # 添加偏移量并截断到指定范围
    offset = offsets[line_idx]
    x_result = x_raw + offset
    x_result = np.clip(x_result, *range_limits[line_idx])  # 分段截断
    
    return x_result

def compare_fft_point(fft_s, fft_d, row_idx, col_idx):
    """
    对比两个帧在某点的幅度比和相位差，并计算对应的角度值
    fft_s, fft_d: shape=(1024, 2048)
    """
    val_s = fft_s[row_idx-1, col_idx]  # 转换为0-based索引
    val_d = fft_d[row_idx-1, col_idx]

    amp_s = np.abs(val_s)
    amp_d = np.abs(val_d)
    phase_s = np.angle(val_s)
    phase_d = np.angle(val_d)

    amp_ratio = amp_d / amp_s if amp_s != 0 else np.inf
    phase_diff = np.sign(np.cos(np.angle(np.exp(1j * (phase_d - phase_s)))))

    # 确定折线编号
    line_idx = get_line_index(row_idx)
    
    # 计算对应的角度值
    angle_result = find_x_with_offset(line_idx, phase_diff * amp_ratio)
    
    print(f"点 (row={row_idx}, col={col_idx}) 比较结果：")
    print(f"  幅值比 (D / S): {amp_ratio:.4f}")
    print(f"  相位差（rad）: {phase_diff:.4f}")
    print(f"  对应折线: {line_idx}")
    print(f"  计算角度: {angle_result:.2f}° (范围: {range_limits[line_idx][0]}~{range_limits[line_idx][1]}°)")

    return amp_ratio, phase_diff, angle_result

# 示例主函数
if __name__ == '__main__':
    # 加载MAT数据
    load_and_preprocess_data()
    
    file_path = 'data/3_33294_28506_SHSDDHDD.bin'
    S_head, D_head, S_data, D_data = read_bin_dual_frame(file_path)

    print(f"帧S angle = {S_head['angle']}, 帧D angle = {D_head['angle']}")

    # 加窗 + FFT
    S_windowed = apply_window(S_data)
    D_windowed = apply_window(D_data)
    fft_s = fft_2d(S_windowed)
    fft_d = fft_2d(D_windowed)

    # 测试不同行的情况
    test_points = [
        (300, 1000),   # 折线0
        (600, 1000),   # 折线1
        (800, 1000),   # 折线2
        (900, 1000),   # 折线3
        (1000, 1000)   # 折线4
    ]
    
    for row_idx, col_idx in test_points:
        compare_fft_point(fft_s, fft_d, row_idx, col_idx)
        print("-" * 50)