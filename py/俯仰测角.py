import numpy as np
import struct
import mkl_fft
import matplotlib.pyplot as plt

# 帧头结构定义（40字节）
frame_header_struct = struct.Struct('<IHHHBBHHBBHIH14s')
SAMPLES_PER_FRAME = 2048  # 每帧2048个复数样本
BYTES_PER_FRAME = 40 + 4096 * 4 * 1024  # 帧头 + 一帧1024x2048 float32

def parse_frame_header(header_bytes):
    unpacked = frame_header_struct.unpack(header_bytes)
    header = {
        'marker': unpacked[0],
        'channel': unpacked[1],
        'circle_num': unpacked[2],
        'frame_num': unpacked[3],
        'task_type': unpacked[4],
        'group_id': unpacked[5],
        'group_limit': unpacked[6],
        'pulse_id': unpacked[7],
        'waveform': unpacked[8],
        'waveform_type': unpacked[9],
        'frame_length': unpacked[10],
        'angle': unpacked[11],
        'angle_counter': unpacked[12],
        'reserved': unpacked[13],
    }
    return header

def read_bin_dual_frame(file_path):
    """读取两个帧，返回 S_head, D_head, S_data, D_data"""
    with open(file_path, 'rb') as f:
        headers = []
        datas = []
        for _ in range(2):
            header_bytes = f.read(40)
            if len(header_bytes) < 40:
                raise ValueError("文件中帧头不足")
            header = parse_frame_header(header_bytes)
            headers.append(header)

            data_bytes = f.read(4096 * 4 * 1024)
            if len(data_bytes) < 4096 * 4 * 1024:
                raise ValueError("文件中帧数据不足")
            float_data = np.frombuffer(data_bytes, dtype=np.float32)
            complex_data = float_data[0::2] + 1j * float_data[1::2]
            complex_data = complex_data.reshape(1024, 2048)
            datas.append(complex_data)

    return headers[0], headers[1], datas[0], datas[1]

def apply_window(data_2d):
    """在列方向加窗（即对每一行加窗）"""
    window = np.hanning(data_2d.shape[1])  # 长度2048
    return data_2d * window[np.newaxis, :]  # broadcast to (1024, 2048)

def fft_2d(data_2d):
    """对二维复数矩阵的每列做FFT（行方向），结果 shape = (1024, 2048)"""
    return mkl_fft.fft(data_2d, axis=0)

def compare_fft_point(fft_s, fft_d, row_idx, col_idx):
    """
    对比两个帧在某点的幅度比和相位差
    fft_s, fft_d: shape=(1024, 2048)
    """
    val_s = fft_s[row_idx, col_idx]
    val_d = fft_d[row_idx, col_idx]

    amp_s = np.abs(val_s)
    amp_d = np.abs(val_d)
    phase_s = np.angle(val_s)
    phase_d = np.angle(val_d)

    amp_ratio = amp_d / amp_s if amp_s != 0 else np.inf

    # phase_diff = np.angle(np.exp(1j * (phase_d - phase_s)))  # wrap 到 [-π, π]
    phase_diff = np.sign(np.cos(np.angle(np.exp(1j * (phase_d - phase_s)))))

    print(f"点 (row={row_idx}, col={col_idx}) 比较结果：")
    print(f"  幅值比 (D / S): {amp_ratio:.4f}")
    print(f"  相位差（rad）: {phase_diff:.4f}")

    return amp_ratio, phase_diff

# 示例主函数
if __name__ == '__main__':
    file_path = 'data/3_33294_28506_SHSDDHDD.bin'
    S_head, D_head, S_data, D_data = read_bin_dual_frame(file_path)

    print(f"帧S angle = {S_head['angle']}, 帧D angle = {D_head['angle']}")

    # 加窗 + FFT
    S_windowed = apply_window(S_data)
    D_windowed = apply_window(D_data)
    fft_s = fft_2d(S_windowed)
    fft_d = fft_2d(D_windowed)

    # 给定感兴趣点
    row_idx = 300  # 1024行内任意
    col_idx = 1000  # 2048列内任意
    compare_fft_point(fft_s, fft_d, row_idx, col_idx)
