import numpy as np
import matplotlib.pyplot as plt
import scipy.io

# 1. 加载 .mat 文件
mat_data = scipy.io.loadmat('data/AthetaPhi.mat')
AthetaPhi = mat_data['AthetaPhi']  # 提取变量 (73, 6)

# 2. 数据处理
he_cha_bi_fuzhi = AthetaPhi.copy()  # 避免修改原数据
he_cha_bi_fuzhi *= -1  # 整体取负

# 3. 数据重排 (列顺序: 6, 2, 1, 3, 4)
hecha_table = np.column_stack((
    he_cha_bi_fuzhi[:, 5],  # 第6列
    he_cha_bi_fuzhi[:, 1],  # 第2列
    he_cha_bi_fuzhi[:, 0],  # 第1列
    he_cha_bi_fuzhi[:, 2],  # 第3列
    he_cha_bi_fuzhi[:, 3]   # 第4列
))

# 4. 可视化（折线图 + 数据点）
x = np.arange(-3.6, 3.61, 0.1)  # 生成 -3.6 到 3.6 的 73 个点

plt.figure(figsize=(10, 6))
colors = ["r", "b", "k", "g", "m"]
markers = ["o", "s", "^", "D", "P"]  # 不同的点标记样式
labels = ["-12.5", "-7.5", "-2.5", "5", "16.25"]

for i in range(5):
    plt.plot(
        x, 
        hecha_table[:, i], 
        color=colors[i], 
        marker=markers[i], 
        markersize=4,  # 点的大小
        linestyle='-',  # 实线
        linewidth=1,    # 线宽
        label=labels[i]
    )

plt.xlabel("angle")
plt.ylabel("bizhi")
plt.legend()
plt.grid(True)
plt.show()