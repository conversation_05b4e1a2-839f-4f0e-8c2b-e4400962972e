#pragma once
#include <vector>
#include <cstdint>

#define ALGORITHM_NAME "SCR_5000"
#define ALGORITHM_API __attribute__ ((visibility ("default")))

// 版本信息结构体
typedef struct {
    int major;
    int minor;
    int patch;
    const char* version_string;
    const char* build_date;
} AlgorithmVersion;

// 帧头结构定义
typedef struct {
    uint32_t marker;
    uint16_t channel;
    uint16_t circle_num;
    uint16_t frame_num;
    uint8_t  task_type;
    uint8_t  group_id;
    uint16_t group_limit;
    uint16_t pulse_id;
    uint8_t  waveform;
    uint8_t  waveform_type;
    uint16_t frame_length;
    uint32_t angle; // 方位角*100
    uint16_t angle_counter;
    char     reserved[14];
} FrameHeader_Alg;

// GPU雷达数据对象结构体
typedef struct {
    // 和通道数据
    std::vector<FrameHeader_Alg> S_head;  // 和通道帧头数组（1024个）
    std::vector<float> S_data;            // 和通道数据（实虚交错格式）

    // 差通道数据
    std::vector<FrameHeader_Alg> D_head;  // 差通道帧头数组（1024个）
    std::vector<float> D_data;            // 差通道数据（实虚交错格式）

    // 数据维度信息
    int rows;                             // 行数（通常为1024）
    int cols;                             // 列数（通常为2048）

    // 数据状态标志
    bool is_valid;                        // 数据是否有效
} GDDataPathObj_GPU;

// 检测结果结构体
typedef struct {
    float x;                              // x坐标
    float y;                              // y坐标
    float z;                              // z坐标
    float vx;                             // x方向速度
    float vy;                             // y方向速度
    float vz;                             // z方向速度
    float fMV;                            // 径向速度
    float fMR;                            // 径向距离
    float fMA;                            // 目标方位角
    float fME;                            // 目标俯仰角
    uint16_t frame;                       // 帧号
    int row;                              // 原始行坐标
    int col;                              // 原始列坐标
    int type;                             // 目标类型
} DetectionResult;

// 跟踪结果结构体
typedef struct {
    unsigned int id;                      // 目标id
    float vx;                             // x方向速度
    float vy;                             // y方向速度
    float vz;                             // z方向速度
    float x;                              // x坐标
    float y;                              // y坐标
    float z;                              // z坐标
    float fMV;                            // 径向速度
    float fMR;                            // 径向距离
    float fMA;                            // 目标方位角
    float fME;                            // 目标俯仰角
    float fSNR;                           // 信噪比
    float fEn;                            // 峰值点能量
    float fRcs;                           // rcs
    unsigned int type;                    // 目标类型
    unsigned long long FPGATimeLog;       // fpga时间戳
    int PreShow;                          // 上位机轨迹预测值是否显示标识：1：显示， 2：不显示
} TrackingResult;

// 版本接口函数
ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info);

// 目标检测算法
ALGORITHM_API int TargetDetection(
    const GDDataPathObj_GPU* input_data,
    DetectionResult** detection_results,
    int* num_detections
);

// 目标跟踪算法
ALGORITHM_API int TargetTracking(
    const DetectionResult* detection_results,
    int num_detections,
    TrackingResult** tracking_results,
    int* num_tracks
);

// 资源释放函数
ALGORITHM_API void ReleaseDetectionResults(DetectionResult* detection_results);
ALGORITHM_API void ReleaseTrackingResults(TrackingResult* tracking_results);
ALGORITHM_API void ReleaseAllResources();