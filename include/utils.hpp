#pragma once
#include <string>
#include <vector>
#include <complex>
#include <eigen3/Eigen/Dense>


// 点迹跟踪算法输入结构体
struct Point {
    std::array<float,3> position;    // 三维坐标 (x, y, z)
    std::array<float,3> velocity;    // 三维速度分量 (vx, vy, vz)
    int type;                        // 目标类型
    int frame;                       // 帧数
    int label;                       // 标签（用于聚类）
};

// 跟踪结果输出
struct TrackResult {
    Eigen::Vector3f position;
    Eigen::Vector3f velocity;
    int id;
};

// 帧头结构定义
struct FrameHeader {
    uint32_t marker;
    uint16_t channel;
    uint16_t circle_num;
    uint16_t frame_num;
    uint8_t  task_type;
    uint8_t  group_id;
    uint16_t group_limit;
    uint16_t pulse_id;
    uint8_t  waveform;
    uint8_t  waveform_type;
    uint16_t frame_length;
    uint32_t angle; // 方位角*100
    uint16_t angle_counter;
    char     reserved[14];
};

// 输出结构体定义
struct TargetInfo {
    unsigned int id;                    // 目标id
    float vx;                           // x方向速度
    float vy;                           // y方向速度
    float vz;                           // z方向速度
    float x;                            // x坐标
    float y;                            // y坐标
    float z;                            // z坐标
    float fMV;                          // 径向速度
    float fMR;                          // 径向距离
    float fMA;                          // 目标方位角
    float fME;                          // 目标俯仰角
    float fSNR;                         // 信噪比
    float fEn;                          // 峰值点能量
    float fRcs;                         // rcs
    unsigned int type;                  // 目标类型
    unsigned long long FPGATimeLog;     // fpga时间戳
    int PreShow;                        // 上位机轨迹预测值是否显示标识：1：显示， 2：不显示
};

// struct GDDataPathObj_GPU {
//     // 和通道数据
//     std::vector<FrameHeader> S_head;  // 和通道帧头数组（1024个）
//     std::vector<float> S_data;            // 和通道数据（实虚交错格式）

//     // 差通道数据
//     std::vector<FrameHeader> D_head;  // 差通道帧头数组（1024个）
//     std::vector<float> D_data;            // 差通道数据（实虚交错格式）

//     // 数据维度信息
//     int rows;                             // 行数（通常为1024）
//     int cols;                             // 列数（通常为2048）

//     // 时间戳信息
//     // uint64_t timestamp;                   // 数据时间戳

//     // 数据状态标志
//     bool is_valid;                        // 数据是否有效
// };

// 查表法相关常量定义
constexpr float X_START = -3.6f;
constexpr float X_END = 3.6f;
constexpr float X_STEP = 0.1f;
// constexpr float offsets[5] = {2.5f, 7.5f, 12.5f, 20.0f, 31.25f};

std::vector<std::string> get_bin_files(const std::string& folder_path);
std::string to_string_6digits(int i);
void initializeCustomPlugins();

// 读取双通道雷达数据
void readDualFrame(
    const std::string& path,
    std::vector<FrameHeader>& S_head,
    std::vector<FrameHeader>& D_head,
    std::vector<float>& S_data,
    std::vector<float>& D_data);

// 3帧雷达数据解析
// bool ExtractFrameToStructured(const std::vector<uint8_t>& merged_bytes, int frame_index, GDDataPathObj_GPU& out);

// 转换为复数
std::vector<std::vector<std::complex<float>>>
convertToComplex(const std::vector<float>& realImagData);

// 查表
extern std::vector<std::vector<float>> hecha_table;     // 预加载的数据表（每列是偏差曲线）
extern std::vector<float> x_axis;
extern std::vector<float> offsets;
extern std::vector<std::vector<float>> rang_limits;

void loadHechaTable_(const std::string& path);
int getLineIndex_(int row_idx);
float findXWithOffset_(int line_idx, float y_target);

// FFT处理
void performFFT2D_(const std::vector<std::vector<std::complex<float>>>& data,
                  std::vector<std::vector<std::complex<float>>>& fft_result);

// 速度换算
float calcDopplerSpeedFromIndex(int y_idx);

// 俯仰角计算
std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>>
computeElevationAngles_(const std::vector<FrameHeader>& S_head,
    const std::vector<std::vector<std::complex<float>>>& S_data,
    const std::vector<std::vector<std::complex<float>>>& D_data,
    const std::vector<std::pair<int, int>>& test_points);

// 聚类算法
std::vector<Point> clusterDetections(const std::vector<Point>& points, float eps);

// 匹配算法
std::vector<std::pair<int, int>> hungarianMatch(const Eigen::MatrixXf& cost);

// 模拟数据生成函数（用于测试）
std::vector<std::tuple<float, float, float, float, float, float, float, float, float, float, uint16_t, int, int>>
generateSimulatedResults(int num_targets, int frame_number, float noise_level = 0.1f);

// 从CSV文件读取模拟数据
std::vector<Point> loadSimulationDataFromCSV(const std::string& csv_path);

// 单个 Track 转换为 TargetInfo
TargetInfo convertTrackToTargetInfo(const TrackResult & track);

// 将 tracks 向量转换为 TargetInfo 向量
std::vector<TargetInfo> convertTracksToTargetInfos(const std::vector<TrackResult>& tracks);